#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源路由管理器

根据章节类型智能选择和路由数据源，实现数据源的优先级管理
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from .generation_models import (
    DataSourceType, SectionDataRequirement, DataSourceMapping,
    SectionGenerationConfig
)

logger = logging.getLogger(__name__)


class DataSourceRouter:
    """数据源路由管理器"""
    
    def __init__(self):
        """初始化数据源路由器"""
        self.section_data_requirements = self._initialize_section_requirements()
        self.data_source_mappings = self._initialize_data_source_mappings()
        self.fallback_strategies = self._initialize_fallback_strategies()
        
    def _initialize_section_requirements(self) -> Dict[str, SectionDataRequirement]:
        """初始化章节数据需求配置"""
        requirements = {}
        
        # introduction章节数据需求
        requirements["introduction"] = SectionDataRequirement(
            section_type="introduction",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.GITHUB, DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.GITHUB: 7,
                DataSourceType.COMMUNITY: 5
            },
            specific_data_fields=[
                "project_metadata", "tech_stack", "structure", "modules"
            ],
            minimum_data_quality=0.8
        )
        
        # installation章节数据需求
        requirements["installation"] = SectionDataRequirement(
            section_type="installation",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE, DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 8,
                DataSourceType.COMMUNITY: 6
            },
            specific_data_fields=[
                "dependencies", "tech_stack", "structure"
            ],
            minimum_data_quality=0.9
        )
        
        # usage章节数据需求
        requirements["usage"] = SectionDataRequirement(
            section_type="usage",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.GITHUB, DataSourceType.TEMPLATE],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.GITHUB: 8,
                DataSourceType.TEMPLATE: 7
            },
            specific_data_fields=[
                "api_design", "modules", "structure"
            ],
            minimum_data_quality=0.8
        )
        
        # api_reference章节数据需求
        requirements["api_reference"] = SectionDataRequirement(
            section_type="api_reference",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 6
            },
            specific_data_fields=[
                "api_design", "modules"
            ],
            minimum_data_quality=0.95
        )
        
        # architecture章节数据需求
        requirements["architecture"] = SectionDataRequirement(
            section_type="architecture",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE, DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 7,
                DataSourceType.COMMUNITY: 5
            },
            specific_data_fields=[
                "architecture", "structure", "modules"
            ],
            minimum_data_quality=0.8
        )
        
        # features章节数据需求
        requirements["features"] = SectionDataRequirement(
            section_type="features",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.GITHUB, DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.GITHUB: 7,
                DataSourceType.COMMUNITY: 6
            },
            specific_data_fields=[
                "modules", "api_design", "tech_stack"
            ],
            minimum_data_quality=0.7
        )
        
        # dependencies章节数据需求
        requirements["dependencies"] = SectionDataRequirement(
            section_type="dependencies",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.COMMUNITY: 6
            },
            specific_data_fields=[
                "dependencies", "tech_stack"
            ],
            minimum_data_quality=0.9
        )
        
        # configuration章节数据需求
        requirements["configuration"] = SectionDataRequirement(
            section_type="configuration",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 7
            },
            specific_data_fields=[
                "structure", "dependencies"
            ],
            minimum_data_quality=0.8
        )
        
        # deployment章节数据需求
        requirements["deployment"] = SectionDataRequirement(
            section_type="deployment",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE, DataSourceType.COMMUNITY],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 8,
                DataSourceType.COMMUNITY: 6
            },
            specific_data_fields=[
                "structure", "dependencies", "tech_stack"
            ],
            minimum_data_quality=0.8
        )
        
        # testing章节数据需求
        requirements["testing"] = SectionDataRequirement(
            section_type="testing",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE],
            data_priority={
                DataSourceType.INTERNAL: 10,
                DataSourceType.TEMPLATE: 7
            },
            specific_data_fields=[
                "structure", "modules"
            ],
            minimum_data_quality=0.8
        )
        
        # contributing章节数据需求
        requirements["contributing"] = SectionDataRequirement(
            section_type="contributing",
            required_data_sources=[DataSourceType.INTERNAL],
            optional_data_sources=[DataSourceType.TEMPLATE, DataSourceType.GITHUB],
            data_priority={
                DataSourceType.INTERNAL: 8,
                DataSourceType.TEMPLATE: 9,
                DataSourceType.GITHUB: 7
            },
            specific_data_fields=[
                "structure", "tech_stack"
            ],
            minimum_data_quality=0.7
        )
        
        # troubleshooting章节数据需求
        requirements["troubleshooting"] = SectionDataRequirement(
            section_type="troubleshooting",
            required_data_sources=[DataSourceType.TEMPLATE],
            optional_data_sources=[DataSourceType.COMMUNITY, DataSourceType.INTERNAL],
            data_priority={
                DataSourceType.TEMPLATE: 9,
                DataSourceType.COMMUNITY: 8,
                DataSourceType.INTERNAL: 6
            },
            specific_data_fields=[
                "tech_stack", "dependencies"
            ],
            minimum_data_quality=0.6
        )
        
        return requirements
    
    def _initialize_data_source_mappings(self) -> Dict[str, DataSourceMapping]:
        """初始化数据源映射配置"""
        mappings = {}
        
        for section_type, requirement in self.section_data_requirements.items():
            primary_source = requirement.required_data_sources[0] if requirement.required_data_sources else DataSourceType.TEMPLATE
            
            mappings[section_type] = DataSourceMapping(
                section_type=section_type,
                primary_data_source=primary_source,
                secondary_data_sources=requirement.optional_data_sources,
                data_extraction_rules={
                    "priority_order": [ds.value for ds in sorted(
                        requirement.data_priority.keys(),
                        key=lambda x: requirement.data_priority[x],
                        reverse=True
                    )],
                    "required_fields": requirement.specific_data_fields,
                    "quality_threshold": str(requirement.minimum_data_quality)
                },
                fallback_strategy="use_template_with_basic_data"
            )
        
        return mappings
    
    def _initialize_fallback_strategies(self) -> Dict[str, str]:
        """初始化回退策略"""
        return {
            "use_template": "使用通用模板生成内容",
            "use_template_with_basic_data": "使用模板结合基础数据生成内容",
            "skip_section": "跳过该章节",
            "use_minimal_content": "生成最小化内容",
            "merge_similar_sections": "合并相似章节"
        }
    
    def get_data_sources_for_section(
        self, 
        section_type: str, 
        available_data_sources: List[DataSourceType]
    ) -> Tuple[List[DataSourceType], Dict[str, Any]]:
        """
        为指定章节类型获取最佳数据源组合
        
        Args:
            section_type: 章节类型
            available_data_sources: 可用的数据源列表
            
        Returns:
            Tuple[List[DataSourceType], Dict[str, Any]]: (选择的数据源, 提取配置)
        """
        if section_type not in self.section_data_requirements:
            logger.warning(f"未知的章节类型: {section_type}")
            return [DataSourceType.TEMPLATE], {}
        
        requirement = self.section_data_requirements[section_type]
        mapping = self.data_source_mappings[section_type]
        
        # 按优先级排序可用数据源
        available_prioritized = []
        for ds in available_data_sources:
            if ds in requirement.data_priority:
                available_prioritized.append((ds, requirement.data_priority[ds]))
        
        available_prioritized.sort(key=lambda x: x[1], reverse=True)
        selected_sources = [ds for ds, _ in available_prioritized]
        
        # 确保至少有一个必需的数据源
        has_required = any(ds in requirement.required_data_sources for ds in selected_sources)
        if not has_required and requirement.required_data_sources:
            # 添加第一个必需的数据源（如果可用）
            for req_ds in requirement.required_data_sources:
                if req_ds in available_data_sources:
                    selected_sources.insert(0, req_ds)
                    break
        
        # 如果仍然没有合适的数据源，使用模板
        if not selected_sources:
            selected_sources = [DataSourceType.TEMPLATE]
        
        extraction_config = {
            "required_fields": requirement.specific_data_fields,
            "quality_threshold": requirement.minimum_data_quality,
            "fallback_strategy": mapping.fallback_strategy,
            "data_extraction_rules": mapping.data_extraction_rules
        }
        
        logger.info(f"为章节 {section_type} 选择数据源: {[ds.value for ds in selected_sources]}")
        
        return selected_sources, extraction_config
    
    def validate_data_quality(
        self, 
        section_type: str, 
        data: Dict[str, Any]
    ) -> Tuple[bool, float, List[str]]:
        """
        验证数据质量是否满足章节要求
        
        Args:
            section_type: 章节类型
            data: 数据内容
            
        Returns:
            Tuple[bool, float, List[str]]: (是否通过, 质量评分, 问题列表)
        """
        if section_type not in self.section_data_requirements:
            return False, 0.0, [f"未知的章节类型: {section_type}"]
        
        requirement = self.section_data_requirements[section_type]
        issues = []
        quality_score = 1.0
        
        # 检查必需字段
        missing_fields = []
        for field in requirement.specific_data_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            quality_score *= 0.5
            issues.append(f"缺少必需字段: {', '.join(missing_fields)}")
        
        # 检查数据完整性
        empty_fields = []
        for field in requirement.specific_data_fields:
            if field in data:
                if isinstance(data[field], (str, dict, list)):
                    if not data[field] or (isinstance(data[field], str) and data[field].strip() == ""):
                        empty_fields.append(field)
        
        if empty_fields:
            quality_score *= 0.8
            issues.append(f"字段内容为空: {', '.join(empty_fields)}")
        
        # 检查数据格式
        for field in requirement.specific_data_fields:
            if field in data and isinstance(data[field], str):
                if len(data[field]) < 10:  # 内容过短
                    quality_score *= 0.9
                    issues.append(f"字段 {field} 内容过短")
        
        passed = quality_score >= requirement.minimum_data_quality
        
        return passed, quality_score, issues
