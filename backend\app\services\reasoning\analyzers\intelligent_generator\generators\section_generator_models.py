#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节生成器数据模型

定义章节生成相关的数据结构和配置
"""
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field
from enum import Enum


class SectionType(str, Enum):
    """章节类型枚举"""
    INTRODUCTION = "introduction"
    FEATURES = "features"
    INSTALLATION = "installation"
    USAGE = "usage"
    API_REFERENCE = "api_reference"
    ARCHITECTURE = "architecture"
    DEPENDENCIES = "dependencies"
    CONTRIBUTING = "contributing"
    LICENSE = "license"
    CHANGELOG = "changelog"
    EXAMPLES = "examples"
    TROUBLESHOOTING = "troubleshooting"
    DEPLOYMENT = "deployment"
    CONFIGURATION = "configuration"
    TESTING = "testing"


class DataSourceRequirement(BaseModel):
    """数据源需求模型"""
    source_type: Literal[
        "project_metadata", "tech_stack", "dependencies", "architecture",
        "modules", "api_design", "structure", "file_organization"
    ] = Field(..., description="数据源类型")
    required: bool = Field(default=True, description="是否必需")
    priority: int = Field(default=5, description="优先级(1-10)")
    description: str = Field(default="", description="数据源用途描述")


class SectionGenerationConfig(BaseModel):
    """章节生成配置"""
    section_type: SectionType = Field(..., description="章节类型")
    title: str = Field(..., description="章节标题")
    description: str = Field(default="", description="章节描述")
    required_data_sources: List[DataSourceRequirement] = Field(
        default_factory=list, description="必需的数据源"
    )
    optional_data_sources: List[DataSourceRequirement] = Field(
        default_factory=list, description="可选的数据源"
    )
    content_requirements: List[str] = Field(
        default_factory=list, description="内容要求"
    )
    format_specifications: Dict[str, Any] = Field(
        default_factory=dict, description="格式规范"
    )
    quality_criteria: List[str] = Field(
        default_factory=list, description="质量标准"
    )
    estimated_length: Literal["short", "medium", "long"] = Field(
        default="medium", description="预估长度"
    )
    priority: int = Field(default=5, description="生成优先级")
    dependencies: List[str] = Field(
        default_factory=list, description="依赖的其他章节"
    )


class SectionContent(BaseModel):
    """章节内容模型"""
    section_type: SectionType = Field(..., description="章节类型")
    title: str = Field(..., description="章节标题")
    content: str = Field(..., description="章节内容(Markdown格式)")
    subsections: List[Dict[str, str]] = Field(
        default_factory=list, description="子章节列表"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="章节元数据"
    )
    quality_score: float = Field(default=0.0, description="内容质量评分")
    generation_notes: List[str] = Field(
        default_factory=list, description="生成说明"
    )
    improvement_suggestions: List[str] = Field(
        default_factory=list, description="改进建议"
    )


class SectionGenerationRequest(BaseModel):
    """章节生成请求"""
    section_config: SectionGenerationConfig = Field(..., description="章节配置")
    project_data: Dict[str, Any] = Field(..., description="项目数据")
    context: Dict[str, Any] = Field(default_factory=dict, description="生成上下文")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="约束条件")


class SectionGenerationResponse(BaseModel):
    """章节生成响应"""
    success: bool = Field(default=True, description="是否成功")
    section_content: Optional[SectionContent] = Field(None, description="生成的章节内容")
    error_message: str = Field(default="", description="错误信息")
    processing_time: float = Field(default=0.0, description="处理时间(秒)")
    data_sources_used: List[str] = Field(default_factory=list, description="使用的数据源")
    ai_reasoning: str = Field(default="", description="AI推理过程")
    confidence_score: float = Field(default=0.0, description="置信度评分(0-1)")


class PromptTemplate(BaseModel):
    """提示词模板"""
    section_type: SectionType = Field(..., description="章节类型")
    template_name: str = Field(..., description="模板名称")
    system_prompt: str = Field(..., description="系统提示词")
    human_prompt: str = Field(..., description="人类提示词")
    variables: List[str] = Field(default_factory=list, description="模板变量")
    format_instructions: str = Field(default="", description="格式指令")
    quality_requirements: List[str] = Field(
        default_factory=list, description="质量要求"
    )
    examples: List[Dict[str, str]] = Field(
        default_factory=list, description="示例"
    )


class SectionGeneratorMetrics(BaseModel):
    """章节生成器指标"""
    section_type: SectionType = Field(..., description="章节类型")
    total_generations: int = Field(default=0, description="总生成次数")
    successful_generations: int = Field(default=0, description="成功生成次数")
    average_processing_time: float = Field(default=0.0, description="平均处理时间")
    average_quality_score: float = Field(default=0.0, description="平均质量评分")
    average_confidence_score: float = Field(default=0.0, description="平均置信度")
    common_errors: List[str] = Field(default_factory=list, description="常见错误")
    improvement_areas: List[str] = Field(default_factory=list, description="改进领域")


# 预定义的章节生成配置
SECTION_CONFIGS = {
    SectionType.INTRODUCTION: SectionGenerationConfig(
        section_type=SectionType.INTRODUCTION,
        title="项目介绍",
        description="项目概述、目标和主要特性",
        required_data_sources=[
            DataSourceRequirement(source_type="project_metadata", priority=10),
            DataSourceRequirement(source_type="tech_stack", priority=9),
            DataSourceRequirement(source_type="structure", priority=8)
        ],
        optional_data_sources=[
            DataSourceRequirement(source_type="architecture", priority=6),
            DataSourceRequirement(source_type="dependencies", priority=5)
        ],
        content_requirements=[
            "项目简介和目标",
            "主要功能特性",
            "技术栈概述",
            "项目状态和版本信息"
        ],
        quality_criteria=[
            "内容简洁明了",
            "突出项目价值",
            "技术信息准确",
            "吸引目标用户"
        ],
        estimated_length="medium",
        priority=10
    ),
    
    SectionType.INSTALLATION: SectionGenerationConfig(
        section_type=SectionType.INSTALLATION,
        title="安装指南",
        description="详细的安装步骤和环境要求",
        required_data_sources=[
            DataSourceRequirement(source_type="dependencies", priority=10),
            DataSourceRequirement(source_type="tech_stack", priority=9),
            DataSourceRequirement(source_type="structure", priority=8)
        ],
        optional_data_sources=[
            DataSourceRequirement(source_type="project_metadata", priority=6)
        ],
        content_requirements=[
            "环境要求和前置条件",
            "安装步骤详解",
            "依赖管理说明",
            "安装验证方法"
        ],
        quality_criteria=[
            "步骤清晰可操作",
            "覆盖不同环境",
            "包含故障排除",
            "提供验证方法"
        ],
        estimated_length="medium",
        priority=9
    ),
    
    SectionType.USAGE: SectionGenerationConfig(
        section_type=SectionType.USAGE,
        title="使用说明",
        description="基本使用方法和常见用例",
        required_data_sources=[
            DataSourceRequirement(source_type="modules", priority=10),
            DataSourceRequirement(source_type="api_design", priority=9),
            DataSourceRequirement(source_type="structure", priority=8)
        ],
        optional_data_sources=[
            DataSourceRequirement(source_type="project_metadata", priority=6),
            DataSourceRequirement(source_type="tech_stack", priority=5)
        ],
        content_requirements=[
            "快速开始指南",
            "基本使用方法",
            "常见用例示例",
            "最佳实践建议"
        ],
        quality_criteria=[
            "示例代码可运行",
            "用例覆盖全面",
            "说明简洁易懂",
            "提供最佳实践"
        ],
        estimated_length="long",
        priority=9
    ),
    
    SectionType.API_REFERENCE: SectionGenerationConfig(
        section_type=SectionType.API_REFERENCE,
        title="API参考",
        description="详细的API文档和接口说明",
        required_data_sources=[
            DataSourceRequirement(source_type="api_design", priority=10),
            DataSourceRequirement(source_type="modules", priority=9)
        ],
        optional_data_sources=[
            DataSourceRequirement(source_type="tech_stack", priority=6),
            DataSourceRequirement(source_type="structure", priority=5)
        ],
        content_requirements=[
            "API接口列表",
            "参数说明详解",
            "返回值格式",
            "示例请求响应"
        ],
        quality_criteria=[
            "接口信息完整",
            "参数说明准确",
            "示例代码正确",
            "格式规范统一"
        ],
        estimated_length="long",
        priority=8
    )
}
