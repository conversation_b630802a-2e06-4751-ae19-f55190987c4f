#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节生成器数据模型

定义章节生成相关的数据结构，严格使用Pydantic模型
"""
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field


class SectionContent(BaseModel):
    """章节内容模型"""
    title: str = Field(..., description="章节标题")
    content: str = Field(..., description="章节内容")
    subsections: List["SectionContent"] = Field(default_factory=list, description="子章节列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="章节元数据")
    
    class Config:
        # 允许递归模型
        arbitrary_types_allowed = True


class GenerationContext(BaseModel):
    """生成上下文模型"""
    project_name: str = Field(..., description="项目名称")
    project_type: str = Field(..., description="项目类型")
    primary_language: str = Field(..., description="主要编程语言")
    target_audience: Literal["developers", "end_users", "both"] = Field(default="developers", description="目标受众")
    complexity_level: Literal["simple", "moderate", "complex"] = Field(default="moderate", description="复杂度级别")
    frameworks: List[str] = Field(default_factory=list, description="使用的框架")
    databases: List[str] = Field(default_factory=list, description="使用的数据库")
    has_api: bool = Field(default=False, description="是否有API")
    has_cli: bool = Field(default=False, description="是否有CLI")
    has_web_interface: bool = Field(default=False, description="是否有Web界面")
    has_tests: bool = Field(default=False, description="是否有测试")
    has_docker: bool = Field(default=False, description="是否有Docker")


class IntroductionGeneratorInput(BaseModel):
    """项目介绍生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    project_metadata: Dict[str, Any] = Field(..., description="项目元数据")
    tech_stack: Dict[str, Any] = Field(..., description="技术栈信息")
    structure_summary: Dict[str, Any] = Field(..., description="项目结构摘要")
    content_requirements: List[str] = Field(default_factory=list, description="内容要求")


class InstallationGeneratorInput(BaseModel):
    """安装指南生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    dependencies: Dict[str, Any] = Field(..., description="依赖信息")
    build_configs: Dict[str, Any] = Field(..., description="构建配置")
    deployment_info: Dict[str, Any] = Field(..., description="部署信息")
    environment_requirements: List[str] = Field(default_factory=list, description="环境要求")


class UsageGeneratorInput(BaseModel):
    """使用说明生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    modules: Dict[str, Any] = Field(..., description="模块信息")
    api_design: Dict[str, Any] = Field(..., description="API设计信息")
    entry_points: List[Dict[str, Any]] = Field(default_factory=list, description="入口点信息")
    examples: List[str] = Field(default_factory=list, description="示例代码")


class ApiReferenceGeneratorInput(BaseModel):
    """API参考生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    api_design: Dict[str, Any] = Field(..., description="API设计信息")
    modules: Dict[str, Any] = Field(..., description="模块信息")
    endpoints: List[Dict[str, Any]] = Field(default_factory=list, description="API端点信息")
    authentication_info: Dict[str, Any] = Field(default_factory=dict, description="认证信息")


class ArchitectureGeneratorInput(BaseModel):
    """架构说明生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    architecture: Dict[str, Any] = Field(..., description="架构信息")
    modules: Dict[str, Any] = Field(..., description="模块信息")
    dependencies: Dict[str, Any] = Field(..., description="依赖信息")
    design_patterns: List[str] = Field(default_factory=list, description="设计模式")


class FeaturesGeneratorInput(BaseModel):
    """功能特性生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    modules: Dict[str, Any] = Field(..., description="模块信息")
    api_design: Dict[str, Any] = Field(..., description="API设计信息")
    tech_stack: Dict[str, Any] = Field(..., description="技术栈信息")
    capabilities: List[str] = Field(default_factory=list, description="功能能力")


class ConfigurationGeneratorInput(BaseModel):
    """配置说明生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    structure: Dict[str, Any] = Field(..., description="项目结构信息")
    config_files: List[Dict[str, Any]] = Field(default_factory=list, description="配置文件信息")
    environment_variables: List[str] = Field(default_factory=list, description="环境变量")


class DeploymentGeneratorInput(BaseModel):
    """部署指南生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    build_configs: Dict[str, Any] = Field(..., description="构建配置")
    dependencies: Dict[str, Any] = Field(..., description="依赖信息")
    deployment_platforms: List[str] = Field(default_factory=list, description="部署平台")
    docker_info: Dict[str, Any] = Field(default_factory=dict, description="Docker信息")


class TestingGeneratorInput(BaseModel):
    """测试指南生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    structure: Dict[str, Any] = Field(..., description="项目结构信息")
    test_configs: List[Dict[str, Any]] = Field(default_factory=list, description="测试配置")
    coverage_info: Dict[str, Any] = Field(default_factory=dict, description="覆盖率信息")


class ContributingGeneratorInput(BaseModel):
    """贡献指南生成器输入模型"""
    context: GenerationContext = Field(..., description="生成上下文")
    structure: Dict[str, Any] = Field(..., description="项目结构信息")
    development_setup: Dict[str, Any] = Field(..., description="开发环境设置")
    coding_standards: List[str] = Field(default_factory=list, description="编码标准")


class SectionGeneratorRequest(BaseModel):
    """章节生成器请求模型"""
    section_type: Literal[
        "introduction", "features", "installation", "usage", "api_reference",
        "architecture", "configuration", "deployment", "testing", "contributing"
    ] = Field(..., description="章节类型")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    generation_options: Dict[str, Any] = Field(default_factory=dict, description="生成选项")
    priority: int = Field(default=5, description="生成优先级")


class SectionGeneratorResponse(BaseModel):
    """章节生成器响应模型"""
    success: bool = Field(default=True, description="是否成功")
    section_content: Optional[SectionContent] = Field(None, description="生成的章节内容")
    error_message: str = Field(default="", description="错误信息")
    processing_time: float = Field(default=0.0, description="处理时间(秒)")
    quality_score: float = Field(default=0.0, description="质量评分(0-1)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="生成元数据")


class MultiSectionGenerationRequest(BaseModel):
    """多章节生成请求模型"""
    sections: List[SectionGeneratorRequest] = Field(..., description="章节生成请求列表")
    generation_strategy: Literal["sequential", "parallel", "dependency_aware"] = Field(
        default="dependency_aware", description="生成策略"
    )
    max_parallel_sections: int = Field(default=3, description="最大并行章节数")
    timeout_seconds: int = Field(default=300, description="超时时间(秒)")


class MultiSectionGenerationResponse(BaseModel):
    """多章节生成响应模型"""
    success: bool = Field(default=True, description="是否成功")
    completed_sections: List[SectionGeneratorResponse] = Field(default_factory=list, description="已完成的章节")
    failed_sections: List[str] = Field(default_factory=list, description="失败的章节类型")
    total_processing_time: float = Field(default=0.0, description="总处理时间(秒)")
    overall_quality_score: float = Field(default=0.0, description="整体质量评分")
    generation_summary: Dict[str, Any] = Field(default_factory=dict, description="生成摘要")


class SectionDependency(BaseModel):
    """章节依赖关系模型"""
    section_type: str = Field(..., description="章节类型")
    depends_on: List[str] = Field(default_factory=list, description="依赖的章节类型")
    dependency_type: Literal["hard", "soft", "optional"] = Field(default="soft", description="依赖类型")
    description: str = Field(default="", description="依赖描述")


class GenerationPipeline(BaseModel):
    """生成流水线模型"""
    stages: List[List[str]] = Field(..., description="生成阶段，每个阶段包含可并行生成的章节类型")
    dependencies: List[SectionDependency] = Field(default_factory=list, description="章节依赖关系")
    total_estimated_time: float = Field(default=0.0, description="预估总时间(秒)")
    pipeline_metadata: Dict[str, Any] = Field(default_factory=dict, description="流水线元数据")


# 更新递归模型
SectionContent.model_rebuild()
