#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目介绍章节生成器

专门负责生成README文档的项目介绍章节
"""
import logging
from typing import List

from .....ai_agent_core import AgentManager
from .base_section_generator import BaseSectionGenerator

logger = logging.getLogger(__name__)


@AgentManager.register("IntroductionSectionGeneratorAgent")
class IntroductionSectionGenerator(BaseSectionGenerator):
    """
    项目介绍章节生成器
    基于项目元数据和技术栈信息生成项目介绍内容
    """

    def get_section_type(self) -> str:
        """获取章节类型"""
        return "introduction"

    def get_required_data_sources(self) -> List[str]:
        """获取所需的数据源类型"""
        return [
            "project_metadata",
            "tech_stack",
            "structure"
        ]

    def get_section_template(self) -> str:
        """获取章节生成的提示模板"""
        header = self.get_base_template_header()
        footer = self.get_base_template_footer()
        
        specific_content = """
        {%- if project_metadata %}
        ### 项目元数据
        {{ project_metadata }}
        {%- endif %}

        {%- if tech_stack %}
        ### 技术栈信息
        {{ tech_stack }}
        {%- endif %}

        {%- if structure %}
        ### 项目结构信息
        {{ structure }}
        {%- endif %}

        ## 项目介绍章节生成指导

        ### 内容结构建议
        1. **项目概述**: 简洁明了地介绍项目的核心功能和目标
        2. **主要特性**: 列出项目的关键特性和亮点
        3. **技术栈**: 介绍使用的主要技术和框架
        4. **项目状态**: 说明项目的当前状态（开发中、稳定版本等）
        5. **适用场景**: 描述项目的典型使用场景和目标用户

        ### 写作要点
        - 开头用1-2句话概括项目的核心价值
        - 突出项目的独特性和优势
        - 使用简洁的语言，避免过于技术性的术语
        - 包含项目的主要编程语言和核心框架
        - 如果有特殊的架构设计或创新点，简要提及

        ### 示例结构
        ```markdown
        # 项目名称

        [项目简介 - 1-2句话概括]

        ## 主要特性

        - 特性1: 简要描述
        - 特性2: 简要描述
        - 特性3: 简要描述

        ## 技术栈

        - **主要语言**: [语言名称]
        - **核心框架**: [框架列表]
        - **数据库**: [数据库类型]
        - **部署方式**: [部署信息]

        ## 项目状态

        [当前开发状态和版本信息]
        ```

        ### 特殊考虑
        - 如果是库/包项目，重点介绍提供的功能和API
        - 如果是应用项目，重点介绍解决的问题和用户价值
        - 如果是框架项目，重点介绍设计理念和扩展性
        - 如果是工具项目，重点介绍使用场景和效率提升
        """
        
        return header + specific_content + footer
